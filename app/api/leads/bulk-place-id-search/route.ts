import { NextResponse } from "next/server";
import { areSimilar } from "@/lib/string-similarity";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchText";

// Delay function to avoid hitting rate limits
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

interface LeadWithoutPlaceId {
  id: number;
  name: string;
  formatted_address: string;
  street_name: string;
  street_number: string;
  city: string;
  postal_code: string;
  canton: string;
  country: string;
  international_phone: string | null;
  national_phone: string | null;
  location: {
    latitude: number;
    longitude: number;
  } | null;
}

interface PlaceIdSearchResult {
  leadId: number;
  leadName: string;
  placeId: string | null;
  googleName: string | null;
  confidence: "high" | "medium" | "low" | "failed";
  searchQuery: string;
  reason?: string;
}

function constructSearchQuery(lead: LeadWithoutPlaceId): string {
  // Build comprehensive search query using available data
  const parts = [];

  if (lead.name) parts.push(lead.name);

  // Add address components
  if (lead.street_name && lead.street_number) {
    parts.push(`${lead.street_number} ${lead.street_name}`);
  } else if (lead.street_name) {
    parts.push(lead.street_name);
  }

  if (lead.city) parts.push(lead.city);
  if (lead.postal_code) parts.push(lead.postal_code);
  if (lead.canton) parts.push(lead.canton);
  if (lead.country) parts.push(lead.country);

  return parts.filter(Boolean).join(", ");
}

function determineConfidence(
  lead: LeadWithoutPlaceId,
  googleName: string,
  hasLocationBias: boolean,
  phoneMatch: boolean
): "high" | "medium" | "low" {
  // Use more lenient thresholds for business names
  const nameSimilarity = areSimilar(lead.name, googleName, 0.7); // Lowered from 0.8
  const highSimilarity = areSimilar(lead.name, googleName, 0.85); // Lowered from 0.9
  const veryHighSimilarity = areSimilar(lead.name, googleName, 0.95);

  if (veryHighSimilarity) {
    return "high"; // Very high similarity is always high confidence
  } else if (highSimilarity && (hasLocationBias || phoneMatch)) {
    return "high";
  } else if (nameSimilarity && hasLocationBias) {
    return "high"; // Good similarity + location bias = high confidence
  } else if (highSimilarity) {
    return "medium"; // High similarity without location bias = medium
  } else if (nameSimilarity) {
    return "medium"; // Decent similarity = medium
  } else {
    return "low";
  }
}

async function searchPlaceId(
  lead: LeadWithoutPlaceId
): Promise<PlaceIdSearchResult> {
  const searchQuery = constructSearchQuery(lead);

  if (!searchQuery) {
    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: null,
      googleName: null,
      confidence: "failed",
      searchQuery: "",
      reason: "Insufficient data for search query",
    };
  }

  try {
    // Prepare request body with location bias if available
    const requestBody: any = {
      textQuery: searchQuery,
      maxResultCount: 3, // Get multiple results to compare
    };

    // Add location bias if coordinates are available
    if (lead.location) {
      requestBody.locationBias = {
        circle: {
          center: {
            latitude: lead.location.latitude,
            longitude: lead.location.longitude,
          },
          radius: 1000, // 1km radius
        },
      };
    }

    const response = await fetch(PLACES_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": GOOGLE_MAPS_API_KEY!,
        // Use ID Only SKU fields as specified
        "X-Goog-FieldMask": "places.attributions,places.id,places.displayName",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Places API error for lead ${lead.id}:`, errorText);
      return {
        leadId: lead.id,
        leadName: lead.name,
        placeId: null,
        googleName: null,
        confidence: "failed",
        searchQuery,
        reason: `API error: ${response.status}`,
      };
    }

    const data = await response.json();
    const places = data.places || [];

    console.log(`\n=== SEARCH DEBUG for "${lead.name}" ===`);
    console.log(`Search Query: "${searchQuery}"`);
    console.log(`Found ${places.length} places from API`);

    if (places.length === 0) {
      console.log(`No places found for "${lead.name}"`);
      return {
        leadId: lead.id,
        leadName: lead.name,
        placeId: null,
        googleName: null,
        confidence: "failed",
        searchQuery,
        reason: "No places found",
      };
    }

    // Find the best match
    let bestMatch = null;
    let bestConfidence: "high" | "medium" | "low" = "low";

    for (let i = 0; i < places.length; i++) {
      const place = places[i];
      // Fix: Use displayName.text instead of name
      const googleName = place.displayName?.text || place.name;

      console.log(`\nPlace ${i + 1}:`);
      console.log(`  Place ID: ${place.id}`);
      console.log(`  Google Name: "${googleName}"`);
      console.log(`  Lead Name: "${lead.name}"`);

      if (!googleName) {
        console.log(`  Skipping - no name available`);
        continue;
      }

      const hasLocationBias = !!lead.location;
      const phoneMatch = false; // We don't have phone data in the response with ID Only SKU

      const confidence = determineConfidence(
        lead,
        googleName,
        hasLocationBias,
        phoneMatch
      );

      console.log(
        `  Confidence: ${confidence} (location bias: ${hasLocationBias})`
      );

      // Log similarity calculation details with debug mode
      console.log(`  Detailed similarity analysis:`);
      const similarity = areSimilar(lead.name, googleName, 0.8, true);
      const highSimilarity = areSimilar(lead.name, googleName, 0.9, false);
      console.log(
        `  Name similarity ≥80%: ${similarity}, ≥90%: ${highSimilarity}`
      );

      if (
        !bestMatch ||
        confidence === "high" ||
        (confidence === "medium" && bestConfidence === "low")
      ) {
        console.log(`  → New best match (previous: ${bestConfidence})`);
        bestMatch = place;
        bestConfidence = confidence;
      }

      // If we found a high confidence match, use it
      if (confidence === "high") {
        console.log(`  → High confidence match found, stopping search`);
        break;
      }
    }

    if (bestMatch) {
      // Fix: Use displayName.text instead of name
      const googleName = bestMatch.displayName?.text || bestMatch.name;

      console.log(`\nFinal result for "${lead.name}":`);
      console.log(`  Best match: "${googleName}"`);
      console.log(`  Confidence: ${bestConfidence}`);

      // Only return Place ID for medium or high confidence matches
      if (bestConfidence === "high" || bestConfidence === "medium") {
        return {
          leadId: lead.id,
          leadName: lead.name,
          placeId: bestMatch.id,
          googleName,
          confidence: bestConfidence,
          searchQuery,
        };
      } else {
        return {
          leadId: lead.id,
          leadName: lead.name,
          placeId: null,
          googleName,
          confidence: "failed",
          searchQuery,
          reason: "Low confidence match - names too different",
        };
      }
    }

    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: null,
      googleName: null,
      confidence: "failed",
      searchQuery,
      reason: "No suitable matches found",
    };
  } catch (error) {
    console.error(`Error searching for lead ${lead.id}:`, error);
    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: null,
      googleName: null,
      confidence: "failed",
      searchQuery,
      reason: `Search error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { leads } = body as { leads: LeadWithoutPlaceId[] };

    if (!leads || !Array.isArray(leads)) {
      return NextResponse.json(
        { error: "Invalid leads data" },
        { status: 400 }
      );
    }

    if (!GOOGLE_MAPS_API_KEY) {
      return NextResponse.json(
        { error: "Google Maps API key not configured" },
        { status: 500 }
      );
    }

    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        let completed = 0;
        let successful = 0;
        let failed = 0;

        try {
          for (const lead of leads) {
            // Search for Place ID
            const result = await searchPlaceId(lead);

            // Update counters
            completed++;
            if (result.placeId) {
              successful++;
            } else {
              failed++;
            }

            // Send progress update
            const progressData = {
              type: "progress",
              completed,
              successful,
              failed,
              total: leads.length,
            };
            controller.enqueue(
              encoder.encode(JSON.stringify(progressData) + "\n")
            );

            // Send result
            const resultData = {
              type: "result",
              result,
            };
            controller.enqueue(
              encoder.encode(JSON.stringify(resultData) + "\n")
            );

            // Add delay to avoid rate limiting (shorter delay as requested)
            await delay(100); // 100ms delay instead of 1000ms
          }

          // Send completion signal
          const completeData = {
            type: "complete",
            totalProcessed: completed,
            totalFound: successful,
            totalFailed: failed,
          };
          controller.enqueue(
            encoder.encode(JSON.stringify(completeData) + "\n")
          );
        } catch (error) {
          console.error("Error in bulk search stream:", error);
          const errorData = {
            type: "error",
            message: error instanceof Error ? error.message : "Unknown error",
          };
          controller.enqueue(encoder.encode(JSON.stringify(errorData) + "\n"));
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    console.error("Error in bulk Place ID search:", error);
    return NextResponse.json(
      {
        error: "Failed to process bulk search",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
